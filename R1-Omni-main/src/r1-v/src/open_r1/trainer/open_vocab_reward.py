# Copyright 2025 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

"""
Enhanced Reward Function for R1-Omni with Difficulty-aware Length Reward
Combines format reward + accuracy reward + difficulty-aware length reward
"""

import re
import os
import sys
import math
from typing import List, Dict, Any, Optional, Union
import torch
import numpy as np

# Add project paths for importing evaluation modules
project_root = "/data/wuyang/AffectGPT-master/OV-MER"
if project_root not in sys.path:
    sys.path.append(project_root)

try:
    from my_affectgpt.evaluation.wheel import (
        read_format2raws,
        read_candidate_synonym_merge,
        func_hit_or_not
    )
    from toolkit.utils.functions import string_to_list
    EW_EVALUATION_AVAILABLE = True
except ImportError:
    EW_EVALUATION_AVAILABLE = False


class EnhancedRewardFunction:
    """
    Enhanced reward function combining format + accuracy + difficulty-aware length rewards.
    """

    def __init__(self,
                 use_ew_evaluation: bool = True,
                 debug_mode: bool = False,
                 format_weight: float = 0.2,
                 accuracy_weight: float = 0.6,
                 length_weight: float = 0.2,
                 difficulty_threshold: float = 0.6):
        """
        Initialize the enhanced reward function.

        Args:
            use_ew_evaluation: Whether to use EW-based evaluation (requires AffectGPT modules)
            debug_mode: Whether to enable debug logging for evaluation details
            format_weight: Weight for format reward (default: 0.2)
            accuracy_weight: Weight for accuracy reward (default: 0.6)
            length_weight: Weight for difficulty-aware length reward (default: 0.2)
            difficulty_threshold: Threshold for updating difficulty labels (default: 0.6)
        """
        self.use_ew_evaluation = use_ew_evaluation and EW_EVALUATION_AVAILABLE
        self._debug_mode = debug_mode

        # Reward weights
        self.format_weight = format_weight
        self.accuracy_weight = accuracy_weight
        self.length_weight = length_weight
        self.difficulty_threshold = difficulty_threshold

        if self.use_ew_evaluation:
            try:
                # Load EW mapping relationships for open vocabulary matching
                self.format_mapping = read_format2raws()
                self.raw_mapping = read_candidate_synonym_merge()
            except Exception as e:
                self.use_ew_evaluation = False

        # Statistics for evaluation tracking
        self.evaluation_stats = {
            'total_evaluations': 0,
            'ew_hits': 0,
            'misses': 0,
            'difficulty_updates': 0
        }

        # Group-level accuracy tracking for difficulty calibration
        self.group_accuracies = {}  # group_id -> list of accuracies



    def update_difficulty_labels(self, group_id: str, accuracy: float) -> str:
        """
        Update difficulty labels based on group accuracy.

        Args:
            group_id: Identifier for the GRPO group
            accuracy: Current accuracy for this group

        Returns:
            Updated difficulty label ('easy' or 'hard')
        """
        if group_id not in self.group_accuracies:
            self.group_accuracies[group_id] = []

        self.group_accuracies[group_id].append(accuracy)

        # Calculate group average accuracy
        group_avg_accuracy = np.mean(self.group_accuracies[group_id])

        # Update difficulty based on threshold
        if group_avg_accuracy > self.difficulty_threshold:
            difficulty = 'easy'
            self.evaluation_stats['difficulty_updates'] += 1
        else:
            difficulty = 'hard'

        return difficulty

    def calculate_difficulty_aware_length_reward(self,
                                               response_length: int,
                                               max_length_in_group: int,
                                               difficulty: str) -> float:
        """
        Calculate difficulty-aware length reward based on the formula:

        For easy problems (ti = [Easy]):
        rl(yi) = 1.0 - (1 - cos((li^j / Li) * π)) / 2

        For hard problems:
        rl(yi) = 0.0

        Args:
            response_length: Length of current response (li^j)
            max_length_in_group: Maximum length in the rollout group (Li)
            difficulty: Difficulty label ('easy' or 'hard')

        Returns:
            Length reward score
        """
        if difficulty != 'easy':
            return 0.0

        if max_length_in_group == 0:
            return 1.0

        # Calculate length ratio
        length_ratio = min(response_length / max_length_in_group, 1.0)

        # Apply cosine-based formula for easy problems
        length_reward = 1.0 - (1 - math.cos(length_ratio * math.pi)) / 2

        return length_reward

    def extract_answer_content(self, text: str) -> Optional[str]:
        """
        Extract answer content from <answer></answer> tags.
        
        Args:
            text: Input text containing answer tags
            
        Returns:
            Extracted answer content or None if not found
        """
        answer_pattern = r'<answer>(.*?)</answer>'
        answer_match = re.search(answer_pattern, text, re.DOTALL)
        if answer_match:
            return answer_match.group(1).strip().lower()
        return None
    
    def extract_think_content(self, text: str) -> Optional[str]:
        """
        Extract thinking content from <think></think> tags.
        
        Args:
            text: Input text containing think tags
            
        Returns:
            Extracted think content or None if not found
        """
        think_pattern = r'<think>(.*?)</think>'
        think_match = re.search(think_pattern, text, re.DOTALL)
        if think_match:
            return think_match.group(1).strip()
        return None
    
    def ew_based_evaluation(self, prediction: str, ground_truth: str) -> bool:
        """
        Use EW-based evaluation to check if prediction matches ground truth.
        Focuses specifically on open vocabulary emotion category hit rate.

        Args:
            prediction: Predicted emotion
            ground_truth: Ground truth emotions (can be a list)

        Returns:
            True if prediction is correct according to EW evaluation
        """
        if not self.use_ew_evaluation:
            return False

        try:
            # Use EW-based evaluation for open vocabulary matching
            # This specifically targets open scenario emotion category hit rate
            hit_result = func_hit_or_not(
                gt_ov=ground_truth,
                pred_ov=prediction,
                metric='case1',  # Use case1 for open vocabulary matching
                format_mapping=self.format_mapping,
                raw_mapping=self.raw_mapping
            )

            return hit_result
        except Exception:
            return False
    
    def compute_accuracy_reward(self, prediction: str, ground_truth: str) -> float:
        """
        Compute accuracy reward for a single prediction with focus on open vocabulary hit rate.

        Args:
            prediction: Predicted emotion
            ground_truth: Ground truth emotions

        Returns:
            Reward score (1.0 for correct, 0.0 for incorrect)
        """
        self.evaluation_stats['total_evaluations'] += 1

        # Try EW-based evaluation first (preferred for open vocabulary)
        if self.use_ew_evaluation:
            is_correct = self.ew_based_evaluation(prediction, ground_truth)
            if is_correct:
                self.evaluation_stats['ew_hits'] += 1
            else:
                self.evaluation_stats['misses'] += 1
        else:
            # Without EW evaluation, default to no match
            is_correct = False
            self.evaluation_stats['misses'] += 1

        # Log statistics periodically for monitoring open vocabulary hit rate
        if self.evaluation_stats['total_evaluations'] % 100 == 0:
            self._log_hit_rate_statistics()

        return 1.0 if is_correct else 0.0

    def _log_hit_rate_statistics(self):
        """Log evaluation statistics for monitoring."""
        pass

    def get_evaluation_summary(self) -> Dict[str, float]:
        """Get summary of evaluation statistics."""
        total = self.evaluation_stats['total_evaluations']
        if total == 0:
            return {'ew_rate': 0.0, 'miss_rate': 0.0, 'difficulty_updates': 0}

        ew_rate = self.evaluation_stats['ew_hits'] / total
        miss_rate = self.evaluation_stats['misses'] / total

        return {
            'ew_rate': ew_rate,
            'miss_rate': miss_rate,
            'difficulty_updates': self.evaluation_stats['difficulty_updates'],
            'total_evaluations': total
        }

    def calculate_format_reward(self, text: str) -> float:
        """
        Compute format reward for proper <think></think> and <answer></answer> structure.

        Args:
            text: Response text to check

        Returns:
            Format reward (1.0 for correct format, 0.0 otherwise)
        """
        pattern = r'<think>.*?</think>\s*<answer>.*?</answer>'
        if re.search(pattern, text, re.DOTALL):
            return 1.0
        return 0.0

    def calculate_accuracy_reward(self, completion_text: str, ground_truth: str) -> float:
        """
        Calculate accuracy reward using open vocabulary matching.

        Args:
            completion_text: Model completion text
            ground_truth: Ground truth label

        Returns:
            Accuracy reward (1.0 for correct, 0.0 for incorrect)
        """
        if ground_truth is None:
            return 0.0

        # Extract answer from completion
        answer = self.extract_answer_content(completion_text)
        if answer is None:
            return 0.0

        # Use open vocabulary evaluation to get accuracy
        accuracy = self.compute_accuracy_reward(answer, ground_truth)
        return accuracy
    
    def __call__(self, completions: List[Union[List[Dict[str, str]], str]], **kwargs) -> List[float]:
        """
        Calculate enhanced rewards for a batch of completions.

        Args:
            completions: List of completions in conversational format or string format
            **kwargs: Additional arguments including:
                - ground_truth/solution: Ground truth labels
                - group_id: GRPO group identifier
                - group_accuracies: Current group accuracies for difficulty calibration

        Returns:
            List of combined reward scores (0.0 to 1.0)
        """
        rewards = []
        ground_truths = kwargs.get('ground_truth', kwargs.get('solution', []))
        group_id = kwargs.get('group_id', 'default')
        group_accuracies = kwargs.get('group_accuracies', [])

        # Handle different input formats
        completion_texts = []
        for comp in completions:
            if isinstance(comp, list) and len(comp) > 0 and isinstance(comp[0], dict):
                # Conversational format: [{"role": "assistant", "content": "..."}]
                completion_texts.append(comp[0]["content"])
            elif isinstance(comp, str):
                # Simple string format
                completion_texts.append(comp)
            else:
                # Fallback: convert to string
                completion_texts.append(str(comp))

        # Calculate max length in this batch for length reward
        response_lengths = [len(text.split()) for text in completion_texts]
        max_length_in_group = max(response_lengths) if response_lengths else 1

        # Extract difficulty information from kwargs - prioritize data difficulty labels
        difficulty_labels = kwargs.get('difficulty', [])  # From dataset
        sample_ids = kwargs.get('sample_ids', [])
        difficulty_calibrator = kwargs.get('difficulty_calibrator', None)

        # Determine difficulty for each completion
        completion_difficulties = []

        for i, completion_text in enumerate(completion_texts):
            # Priority 1: Use difficulty labels from dataset (current labels)
            if i < len(difficulty_labels):
                difficulty = difficulty_labels[i]

            # Priority 2: Use difficulty calibrator with sample ID (updated labels)
            elif difficulty_calibrator and i < len(sample_ids):
                difficulty = difficulty_calibrator.get_difficulty(sample_ids[i])

            # Priority 3: Extract from response text (predicted difficulty)
            elif completion_text.startswith('[') and ']' in completion_text[:10]:
                diff_end = completion_text.find(']')
                extracted_diff = completion_text[1:diff_end].lower()
                difficulty = extracted_diff if extracted_diff in ['easy', 'hard'] else 'hard'

            # Priority 4: Default to hard
            else:
                difficulty = 'hard'

            completion_difficulties.append(difficulty)

        for i, completion_text in enumerate(completion_texts):
            current_difficulty = completion_difficulties[i]

            # Calculate individual reward components
            format_reward = self.calculate_format_reward(completion_text)

            # Calculate accuracy reward
            if i < len(ground_truths):
                accuracy_reward = self.calculate_accuracy_reward(completion_text, ground_truths[i])
            else:
                accuracy_reward = 0.0

            # Calculate difficulty-aware length reward using current difficulty
            length_reward = self.calculate_difficulty_aware_length_reward(
                response_lengths[i], max_length_in_group, current_difficulty
            )

            # Combine rewards with weights
            total_reward = (
                self.format_weight * format_reward +
                self.accuracy_weight * accuracy_reward +
                self.length_weight * length_reward
            )

            rewards.append(total_reward)

        self.evaluation_stats['total_evaluations'] += len(completions)

        # Check if caller requested accuracy information
        return_accuracy_info = kwargs.get('return_accuracy_info', False)
        if return_accuracy_info:
            # Extract accuracy scores from the reward calculation
            accuracy_scores = []
            for i, completion_text in enumerate(completion_texts):
                if i < len(ground_truths):
                    accuracy_score = self.calculate_accuracy_reward(completion_text, ground_truths[i])
                else:
                    accuracy_score = 0.0
                accuracy_scores.append(accuracy_score)

            return rewards, accuracy_scores
        else:
            return rewards


# Create global instance for use in training
enhanced_reward_func = EnhancedRewardFunction(
    use_ew_evaluation=True,
    debug_mode=False,
    format_weight=0.2,
    accuracy_weight=0.6,
    length_weight=0.2,
    difficulty_threshold=0.6
)


def enhanced_emotion_reward(completions, **kwargs):
    """
    Enhanced reward function combining format + accuracy + difficulty-aware length rewards.

    Args:
        completions: List of completions
        **kwargs: Additional arguments including:
            - ground_truth/solution: Ground truth labels
            - sample_ids: List of sample IDs for difficulty lookup (optional)
            - difficulty_labels: List of current difficulty labels from calibrator (optional)
            - difficulty_calibrator: DifficultyCalibrator instance for real-time lookup (optional)
            - format_weight: Weight for format reward (optional)
            - accuracy_weight: Weight for accuracy reward (optional)
            - length_weight: Weight for length reward (optional)
            - difficulty_threshold: Threshold for difficulty calibration (optional, fallback only)

    Returns:
        List of combined reward scores
    """
    # Extract configuration from kwargs if provided
    format_weight = kwargs.pop('format_weight', enhanced_reward_func.format_weight)
    accuracy_weight = kwargs.pop('accuracy_weight', enhanced_reward_func.accuracy_weight)
    length_weight = kwargs.pop('length_weight', enhanced_reward_func.length_weight)

    # Create temporary instance with custom weights if different from default
    if (format_weight != enhanced_reward_func.format_weight or
        accuracy_weight != enhanced_reward_func.accuracy_weight or
        length_weight != enhanced_reward_func.length_weight):

        temp_func = EnhancedRewardFunction(
            use_ew_evaluation=enhanced_reward_func.use_ew_evaluation,
            debug_mode=True,  # Enable debug mode for detailed logging
            format_weight=format_weight,
            accuracy_weight=accuracy_weight,
            length_weight=length_weight,
            difficulty_threshold=enhanced_reward_func.difficulty_threshold  # Keep default
        )
        result = temp_func(completions, **kwargs)
    else:
        # Enable debug mode temporarily
        original_debug = enhanced_reward_func._debug_mode
        enhanced_reward_func._debug_mode = True
        result = enhanced_reward_func(completions, **kwargs)
        enhanced_reward_func._debug_mode = original_debug

    return result


def emotion_accuracy_reward(completions, **kwargs):
    """
    Wrapper function for accuracy-only reward compatible with GRPO trainer.

    Args:
        completions: List of completions
        **kwargs: Additional arguments including ground_truth

    Returns:
        List of accuracy reward scores
    """
    # Create temporary instance with 100% accuracy weight
    temp_func = EnhancedRewardFunction(
        use_ew_evaluation=True,
        format_weight=0.0,
        accuracy_weight=1.0,
        length_weight=0.0
    )
    return temp_func(completions, **kwargs)


def emotion_format_reward(completions, **kwargs):
    """
    Wrapper function for format-only reward compatible with GRPO trainer.

    Args:
        completions: List of completions
        **kwargs: Additional arguments

    Returns:
        List of format reward scores
    """
    # Create temporary instance with 100% format weight
    temp_func = EnhancedRewardFunction(
        format_weight=1.0,
        accuracy_weight=0.0,
        length_weight=0.0
    )
    return temp_func(completions, **kwargs)


def calculate_accuracy_reward_standalone(completion_text: str, ground_truth: str) -> float:
    """
    Standalone function to calculate accuracy reward using the same logic as EnhancedRewardFunction.

    Args:
        completion_text: Model completion text
        ground_truth: Ground truth label

    Returns:
        Accuracy reward (1.0 for correct, 0.0 for incorrect)
    """
    # Use the global enhanced reward function instance
    return enhanced_reward_func.calculate_accuracy_reward(completion_text, ground_truth)


def get_enhanced_reward_statistics():
    """
    Get current enhanced reward statistics.

    Returns:
        Dictionary with evaluation statistics
    """
    return enhanced_reward_func.evaluation_stats
