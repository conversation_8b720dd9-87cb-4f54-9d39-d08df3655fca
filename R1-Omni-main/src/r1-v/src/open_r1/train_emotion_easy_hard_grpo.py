#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Training script for Emotion Recognition with Easy-Hard GRPO
Integrates AdaCtrl's difficulty calibration with R1-Omni's multimodal capabilities
"""

import os
import sys
import json
import torch
from datasets import Dataset
from trl import ModelConfig, ScriptArguments, TrlParser, GRPOConfig
from transformers import TrainingArguments
from dataclasses import dataclass, field
from typing import Optional, List

# Add project paths
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from trainer.humanOmni_grpo_trainer import HumanOmniVLGRPOTrainer
from trainer.open_vocab_reward import (
    enhanced_emotion_reward,
    emotion_accuracy_reward,
    emotion_format_reward,
    get_enhanced_reward_statistics
)


@dataclass
class ExtendedGRPOConfig(GRPOConfig):
    """Extended GRPO config with Easy-Hard specific parameters."""

    # Easy-Hard GRPO parameters
    enable_easy_hard_grpo: bool = field(default=True)
    enable_difficulty_calibration: bool = field(default=True)
    diff_threshold: float = field(default=0.6)
    # Note: difficulty_update_frequency removed - updates happen after each GRPO group
    difficulty_data_path: str = field(default="")
    format_reward_weight: float = field(default=0.15)
    length_reward_weight: float = field(default=0.05)
    calibration_reward_weight: float = field(default=0.05)

    # Format parameters
    think_answer_format: bool = field(default=True)
    difficulty_tag_format: bool = field(default=True)
    max_think_length: int = field(default=256)
    max_answer_length: int = field(default=32)

    # Enhanced reward function parameters
    use_enhanced_reward: bool = field(default=True)
    reward_functions: List[str] = field(default_factory=lambda: ["enhanced_emotion"])
    reward_weights: List[float] = field(default_factory=lambda: [1.0])
    use_ew_evaluation: bool = field(default=True)

    # Enhanced reward component weights
    enhanced_format_weight: float = field(default=0.2)
    enhanced_accuracy_weight: float = field(default=0.6)
    enhanced_length_weight: float = field(default=0.2)
    # Note: difficulty_threshold removed - using dataset difficulty labels directly

    # Adaptive KL parameters
    adaptive_kl: bool = field(default=True)
    init_kl_coef: float = field(default=0.1)
    target_kl: float = field(default=0.1)
    kl_horizon: int = field(default=10000)

    # Vision parameters
    max_pixels: int = field(default=401408)


def load_emotion_dataset(dataset_path: str):
    """
    Load emotion dataset from JSON file with data type cleaning.

    Args:
        dataset_path: Path to dataset JSON file

    Returns:
        Tuple of (train_dataset, eval_dataset)
    """
    with open(dataset_path, 'r', encoding='utf-8') as f:
        data = json.load(f)

    # Clean data types to avoid PyArrow errors
    def clean_data_recursive(obj):
        """Recursively clean data types to avoid PyArrow errors."""
        if isinstance(obj, dict):
            return {key: clean_data_recursive(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [clean_data_recursive(item) for item in obj]
        elif isinstance(obj, (float, bool)):
            # Convert float/bool to string to avoid PyArrow type errors
            return str(obj)
        elif obj is None:
            return ""
        else:
            # Keep strings and other types as they are
            return obj

    def clean_entry(entry):
        """Clean data types in a dataset entry."""
        return clean_data_recursive(entry)

    # Clean and format train and validation data
    def format_entry(entry):
        """Format data entry for GRPO training."""
        cleaned = clean_entry(entry)

        # Video path should already be at top level in the new format
        # No need to extract from prompt structure anymore
        return cleaned

    train_data = [format_entry(entry) for entry in data['train']]
    val_data = [format_entry(entry) for entry in data['validation']]

    train_dataset = Dataset.from_list(train_data)
    eval_dataset = Dataset.from_list(val_data)



    return train_dataset, eval_dataset


# Function removed - using ExtendedGRPOConfig directly



def main():
    """
    Main training function.
    """
    # Parse arguments using extended config
    parser = TrlParser((ScriptArguments, ExtendedGRPOConfig, ModelConfig))
    script_args, training_args, model_config = parser.parse_args_and_config()

    # Parameters are now handled by ExtendedGRPOConfig
    

    
    # Load datasets
    dataset_path = getattr(script_args, 'dataset_name', 'emotion_grpo_dataset.json')
    train_dataset, eval_dataset = load_emotion_dataset(dataset_path)

    # Setup reward functions with detailed logging
    if training_args.use_enhanced_reward:
        # Create configured enhanced reward function with debugging
        def configured_enhanced_reward(completions, **kwargs):
            # Add configuration parameters to kwargs
            kwargs.update({
                'format_weight': training_args.enhanced_format_weight,
                'accuracy_weight': training_args.enhanced_accuracy_weight,
                'length_weight': training_args.enhanced_length_weight,
                'return_accuracy_info': True  # Request accuracy information
            })

            # Call the enhanced reward function
            result = enhanced_emotion_reward(completions, **kwargs)

            # Handle both old format (just rewards) and new format (rewards + accuracy)
            if isinstance(result, tuple) and len(result) == 2:
                rewards, accuracy_info = result
                # Return only rewards - trainer will handle accuracy separately
                return rewards
            else:
                # Fallback to old format
                rewards = result
                return rewards

        reward_funcs = [configured_enhanced_reward]
    else:
        # Use traditional separate reward functions
        reward_funcs = [emotion_accuracy_reward, emotion_format_reward]

    # For distributed training with DeepSpeed, we don't pre-load the model
    # The trainer will handle model loading and distribution
    processing_class = None

    # Check if we're in distributed training mode
    import os
    if 'RANK' in os.environ and 'WORLD_SIZE' in os.environ:
        processing_class = None
    else:
        # Only load model for single GPU training
        from humanomni import model_init
        _, processor_dict, tokenizer = model_init(model_config.model_name_or_path)

        # Create a wrapper class to hold both tokenizer and visual processor
        class HumanOmniProcessingClass:
            def __init__(self, tokenizer, processor_dict):
                self.tokenizer = tokenizer
                self.processor_dict = processor_dict
                # Copy tokenizer attributes for compatibility
                self.pad_token_id = tokenizer.pad_token_id
                self.eos_token_id = tokenizer.eos_token_id

            def batch_decode(self, *args, **kwargs):
                """Delegate batch_decode to the tokenizer"""
                return self.tokenizer.batch_decode(*args, **kwargs)

            def decode(self, *args, **kwargs):
                """Delegate decode to the tokenizer"""
                return self.tokenizer.decode(*args, **kwargs)

        processing_class = HumanOmniProcessingClass(tokenizer, processor_dict)

    # Initialize trainer with model path and correct processing class
    trainer = HumanOmniVLGRPOTrainer(
        model=model_config.model_name_or_path,  # Pass model path, not model object
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=eval_dataset,
        reward_funcs=reward_funcs,
        processing_class=processing_class,  # Use HumanOmni processing class
        max_pixels=401408,  # Same as original grpo.py
        min_pixels=3136,    # Same as original grpo.py
    )
    
    # Check for checkpoint resuming
    resume_from_checkpoint = getattr(training_args, 'resume_from_checkpoint', None)

    # Start training
    try:
        trainer.train(resume_from_checkpoint=resume_from_checkpoint)

        # Save final model
        trainer.save_model()

        # Final evaluation
        eval_results = trainer.evaluate()

        return True

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
